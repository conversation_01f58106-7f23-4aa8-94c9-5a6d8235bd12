<?xml version="1.0" encoding="utf-8"?>
<templates id="template_error" xml:space="preserve">

    <!-- Error Page Template -->
    <t t-name="error.html">
        <html>
            <head>
                <title t-esc="title"/>
                <meta charset="utf-8"/>
                <meta name="viewport" content="width=device-width, initial-scale=1"/>
                <!-- Tailwind CSS CDN -->
                <script src="https://cdn.tailwindcss.com"></script>
                <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
                <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet"/>
            </head>
            <body class="font-sans m-0 p-5 bg-gray-100 min-h-screen">
                <div class="max-w-3xl mx-auto bg-white p-8 rounded-lg shadow-lg">
                    <div class="text-center mb-8 pb-5 border-b-2 border-red-100">
                        <h1 class="text-red-700 m-0 text-4xl font-bold" t-esc="title"/>
                    </div>

                    <div class="bg-red-50 border border-red-200 rounded p-5 my-5">
                        <div class="text-red-700 font-medium mb-2">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            An error occurred while processing your request.
                        </div>
                        <t t-if="error_message">
                            <div class="text-gray-600 font-mono bg-gray-100 p-3 rounded mt-3 break-all text-sm" t-esc="error_message"/>
                        </t>
                    </div>

                    <div class="text-center mt-8">
                        <a href="/app" class="inline-block px-5 py-3 mx-2 no-underline rounded font-medium transition-colors duration-300 bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <i class="fas fa-home mr-2"></i>
                            Go to App
                        </a>
                        <a href="/app/databases" class="inline-block px-5 py-3 mx-2 no-underline rounded font-medium transition-colors duration-300 bg-gray-600 text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500">
                            <i class="fas fa-database mr-2"></i>
                            Database List
                        </a>
                        <button onclick="history.back()" class="inline-block px-5 py-3 mx-2 border-0 rounded font-medium transition-colors duration-300 bg-gray-600 text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 cursor-pointer">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Go Back
                        </button>
                    </div>
                </div>
            </body>
        </html>
    </t>

</templates>
