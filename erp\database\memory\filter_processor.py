"""
Database filtering and processing logic
"""
import re
from typing import Optional, Dict, Any, List
from .registry_manager import MemoryRegistryManager
from ...logging import get_logger


class DatabaseFilterProcessor:
    """
    Processes database filtering and determines when to create memory registries
    """
    
    _logger = get_logger(__name__)
    
    @classmethod
    def check_database_matches_filter(cls, db_name: str, db_filter: Optional[str] = None) -> bool:
        """
        Check if a database name matches the given filter pattern
        
        Args:
            db_name: Database name to check
            db_filter: Database filter pattern (regex)
            
        Returns:
            True if database matches filter or no filter is set, False otherwise
        """
        if not db_filter:
            return True
        
        try:
            return bool(re.match(db_filter, db_name))
        except re.error:
            cls._logger.warning(f"Invalid db_filter pattern: {db_filter}")
            return True
    
    @classmethod
    def filter_databases(cls, databases: list, db_filter: Optional[str] = None) -> list:
        """
        Filter a list of databases based on the given filter pattern
        
        Args:
            databases: List of database names to filter
            db_filter: Database filter pattern (regex)
            
        Returns:
            List of database names that match the filter
        """
        if not db_filter:
            return databases.copy()
        
        filtered = []
        for db_name in databases:
            if cls.check_database_matches_filter(db_name, db_filter):
                filtered.append(db_name)
        
        return filtered
    
    @classmethod
    async def process_database_filter(cls, databases: list, db_filter: Optional[str] = None,
                                    specific_db_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Process database filtering and determine if memory registry should be created
        
        Args:
            databases: List of available databases
            db_filter: Database filter pattern
            specific_db_name: Specific database name if set
            
        Returns:
            Dict containing filtered databases and registry info
        """
        filtered_databases = []
        should_create_registry = False
        registry_db_name = None
        
        # Case 1: Specific database name is set - always create registry (ignore filters)
        if specific_db_name:
            if specific_db_name in databases:
                filtered_databases = [specific_db_name]
                should_create_registry = True
                registry_db_name = specific_db_name
                cls._logger.info(f"Single database mode: {specific_db_name} (filter ignored)")
            else:
                cls._logger.error(f"Specified database {specific_db_name} not found in available databases")
        
        # Case 2: Multi-database mode with filtering (no automatic registry creation)
        else:
            filtered_databases = cls.filter_databases(databases, db_filter)
            
            if db_filter:
                cls._logger.info(f"Multi-database mode: {len(filtered_databases)} databases match filter")
            else:
                cls._logger.info(f"Multi-database mode: {len(filtered_databases)} databases (no filter)")
        
        return {
            'filtered_databases': filtered_databases,
            'should_create_registry': should_create_registry,
            'registry_db_name': registry_db_name,
            'total_available': len(databases),
            'total_filtered': len(filtered_databases)
        }
    
    @classmethod
    async def ensure_registry_for_environment(cls, db_name: str) -> Optional['AppRegistry']:
        """
        Ensure registry exists for environment creation
        Only creates registry if Base module is installed
        """
        from ...config import config
        
        # Get all available databases
        from ..registry.database_registry import DatabaseRegistry
        all_databases = await DatabaseRegistry.list_databases()
        
        # Process filtering
        filter_result = await cls.process_database_filter(
            databases=all_databases,
            db_filter=config.db_filter,
            specific_db_name=config.get_default_database()
        )
        
        # If this database should have a registry, create/get it
        if (filter_result['should_create_registry'] and
            filter_result['registry_db_name'] == db_name):
            
            cls._logger.info(f"Creating/ensuring registry for environment database: {db_name}")
            try:
                return await MemoryRegistryManager.get_registry(db_name)
            except RuntimeError as e:
                cls._logger.warning(f"Cannot create registry for {db_name}: {e}")
                return None
        
        return None
