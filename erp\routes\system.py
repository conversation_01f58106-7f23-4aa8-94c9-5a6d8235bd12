"""
System information and health check routes
"""
from fastapi import Request, HTTPException
from fastapi.responses import HTMLResponse, RedirectResponse

from ..config import config
from ..database.registry import DatabaseRegistry
from ..utils.responses import APIResponse
from ..templates.manager import get_template_manager
from ..http import systemRoute


@systemRoute("/", methods=["GET"], auth="none")
async def root():
    """Root endpoint"""
    # If list_db is enabled, redirect to database list regardless of mode
    if config.list_db:
        return RedirectResponse(url="/databases", status_code=302)
    return APIResponse.success({"message": "ERP System API", "version": "1.0.0"})


@systemRoute("/health", methods=["GET"], auth="none")
async def health_check():
    """Health check endpoint - only checks server status, not database connections"""
    try:
        # Only check server health, no database connections during startup
        return APIResponse.success({
            "status": "healthy",
            "server": "running",
            "message": "ERP server is running"
        })
    except Exception as e:
        return APIResponse.error(f"Health check failed: {str(e)}", 503)


# Removed duplicate /databases route - now handled by erp/routes/database.py
# Only system-level routes and health check are kept here
# Note: /home route is handled by addon controller in addons/base/controllers/home_controller.py