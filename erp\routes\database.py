"""
Database list viewing routes - only list viewing and redirection
"""
from fastapi import Request
from fastapi.responses import HTMLResponse
from typing import Dict, Any, List
import re

from ..config import config
from ..database.registry import DatabaseRegistry
from ..templates.manager import get_template_manager
from ..http import systemRoute


async def get_database_list() -> List[Dict[str, Any]]:
    """Helper function to get database list with information"""
    from ..database.memory import DatabaseFilterProcessor, MemoryRegistryManager
    
    db_info_list = []

    # Get all available databases
    databases = await DatabaseRegistry.list_databases()
    
    # Process database filtering using the new system
    filter_result = await DatabaseFilterProcessor.process_database_filter(
        databases=databases,
        db_filter=config.db_filter,
        specific_db_name=config.get_default_database()
    )
    
    filtered_databases = filter_result['filtered_databases']
    
    # If memory registry should be created, ensure it exists
    if filter_result['should_create_registry'] and filter_result['registry_db_name']:
        registry_db = filter_result['registry_db_name']
        await MemoryRegistryManager.get_registry(registry_db)
        print(f"[INFO] Memory registry ensured for database: {registry_db}")

    # Build database info for filtered databases
    for db_name in filtered_databases:
        # Check if this database has a memory registry
        has_registry = await MemoryRegistryManager.has_registry(db_name)
        
        # Check if base module is installed
        base_module_installed = await MemoryRegistryManager._is_base_module_installed(db_name)
        
        # Determine database initialization status
        if base_module_installed:
            init_status = "ready"
            init_message = "Base module installed - Ready for use"
            init_icon = "fa-check-circle"
            init_color = "green"
        else:
            init_status = "needs_init"
            init_message = "Requires base module installation"
            init_icon = "fa-exclamation-triangle"
            init_color = "yellow"
        
        # Get comprehensive database information
        try:
            db_manager = await DatabaseRegistry.get_database('postgres')

            # Get database size
            size_query = f"SELECT pg_size_pretty(pg_database_size('{db_name}')) as size"
            size_result = await db_manager.fetch(size_query)
            size = size_result[0]['size'] if size_result else 'Unknown'

            # Get creation date (approximate)
            created_query = f"""
                SELECT (pg_stat_file('base/'||oid||'/PG_VERSION')).modification as created
                FROM pg_database WHERE datname = '{db_name}'
            """
            created_result = await db_manager.fetch(created_query)
            created = created_result[0]['created'].isoformat() if created_result else None

            # Get database statistics
            stats_query = f"""
                SELECT
                    d.datname as name,
                    pg_catalog.pg_get_userbyid(d.datdba) as owner,
                    pg_encoding_to_char(d.encoding) as encoding,
                    d.datcollate as collate,
                    d.datctype as ctype,
                    CASE WHEN d.datallowconn THEN 'Yes' ELSE 'No' END as allow_connections,
                    d.datconnlimit as connection_limit,
                    pg_size_pretty(pg_database_size(d.datname)) as size_pretty,
                    pg_database_size(d.datname) as size_bytes
                FROM pg_database d
                WHERE d.datname = '{db_name}'
            """
            stats_result = await db_manager.fetch(stats_query)
            db_stats = stats_result[0] if stats_result else {}

            # Get connection count
            conn_query = f"""
                SELECT count(*) as active_connections
                FROM pg_stat_activity
                WHERE datname = '{db_name}' AND state = 'active'
            """
            conn_result = await db_manager.fetch(conn_query)
            active_connections = conn_result[0]['active_connections'] if conn_result else 0

            # Get table count
            table_query = f"""
                SELECT count(*) as table_count
                FROM information_schema.tables
                WHERE table_catalog = '{db_name}'
                AND table_schema NOT IN ('information_schema', 'pg_catalog')
            """
            table_result = await db_manager.fetch(table_query)
            table_count = table_result[0]['table_count'] if table_result else 0

        except Exception as e:
            # Use logger for consistency
            from erp.logging import get_logger
            logger = get_logger(__name__)
            logger.debug(f"Error getting info for database {db_name}: {e}")
            size = 'Unknown'
            created = None
            db_stats = {}
            active_connections = 0
            table_count = 0

        # Format created date for display
        created_display = None
        if created:
            try:
                from datetime import datetime
                created_dt = datetime.fromisoformat(created.replace('Z', '+00:00'))
                created_display = created_dt.strftime('%Y-%m-%d %H:%M')
            except:
                created_display = created

        db_info_list.append({
            'name': db_name,
            'size': size,
            'created': created,
            'created_display': created_display,
            'owner': db_stats.get('owner', 'erp'),
            'encoding': db_stats.get('encoding', 'UTF8'),
            'collate': db_stats.get('collate', 'Unknown'),
            'ctype': db_stats.get('ctype', 'Unknown'),
            'allow_connections': db_stats.get('allow_connections', 'Yes'),
            'connection_limit': db_stats.get('connection_limit', -1),
            'size_bytes': db_stats.get('size_bytes', 0),
            'active_connections': active_connections,
            'table_count': table_count,
            'status': 'active',  # Default status
            'has_memory_registry': has_registry,
            'registry_info': filter_result if has_registry else None,
            # New initialization status fields
            'base_module_installed': base_module_installed,
            'init_status': init_status,
            'init_message': init_message,
            'init_icon': init_icon,
            'init_color': init_color
        })

    return db_info_list


@systemRoute("/databases", methods=["GET"], auth="none")
async def database_list_page(request: Request):
    """Database list page"""
    template_manager = get_template_manager()

    # Get database list
    databases = await get_database_list()

    # Template context
    context = {
        "request": request,
        "title": "Database Management",
        "databases": databases,
        "config": config,  # Pass the actual config object
        "is_development_mode": config.is_development_mode
    }

    # Template will be loaded automatically if not found
    return template_manager.render_template("system.database_list_enhanced", context)