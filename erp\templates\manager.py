"""
Template Manager - Handles template loading, caching, and resolution
"""
import os
from pathlib import Path
from typing import Dict, Any, Optional, List
import asyncio
from .engine import TemplateEng<PERSON>
from .exceptions import TemplateNotFoundError, TemplateError, TemplateRenderError


class TemplateManager:
    """Manages template loading, caching, and rendering"""

    def __init__(self, template_dirs: Optional[List[str]] = None, enable_caching: bool = True, enable_debugging: bool = False, enable_security: bool = True):
        self.engine = TemplateEngine(enable_caching=enable_caching, enable_debugging=enable_debugging, enable_security=enable_security)
        self.template_dirs = template_dirs or []
        self.template_cache: Dict[str, str] = {}
        self._loaded_files: Dict[str, float] = {}  # filename -> mtime
        self.enable_caching = enable_caching
        self.enable_debugging = enable_debugging
        self.enable_security = enable_security

        # Add default template directories
        self._add_default_template_dirs()
    
    def _add_default_template_dirs(self):
        """Add default template directories"""
        # Add templates directory relative to this module
        current_dir = Path(__file__).parent
        default_template_dir = current_dir / "templates"
        if default_template_dir.exists():
            self.template_dirs.append(str(default_template_dir))

        # Add project templates directory (go up two levels from erp/templates/)
        project_templates = current_dir.parent.parent / "templates"
        if project_templates.exists():
            self.template_dirs.append(str(project_templates))

    def load_all_templates(self):
        """Load all templates from all template directories during startup"""
        loaded_count = 0
        loaded_files = set()  # Track loaded files to avoid duplicates

        for template_dir in self.template_dirs:
            template_path = Path(template_dir)
            if template_path.exists():
                # Find all XML template files
                for xml_file in template_path.glob("**/*.xml"):
                    # Use absolute path to avoid loading the same file twice
                    abs_path = xml_file.resolve()
                    if abs_path in loaded_files:
                        continue  # Skip if already loaded

                    try:
                        with open(xml_file, 'r', encoding='utf-8') as f:
                            content = f.read()

                        # Load templates into engine (this extracts template names from t-name attributes)
                        self.engine.load_template(xml_file.name, content)

                        # Cache the content and track modification time
                        self.template_cache[xml_file.name] = content
                        self._loaded_files[xml_file.name] = xml_file.stat().st_mtime

                        loaded_files.add(abs_path)
                        loaded_count += 1

                    except Exception as e:
                        # Log error but continue loading other templates
                        print(f"Warning: Failed to load template {xml_file}: {e}")

        print(f"Loaded {loaded_count} template files with {len(self.engine.system_templates)} template definitions")
        return loaded_count
    
    def add_template_directory(self, directory: str):
        """Add a template directory to search path"""
        if directory not in self.template_dirs:
            self.template_dirs.append(directory)
    
    def load_template_file(self, filename: str) -> str:
        """
        Load template file content from template directories

        DEPRECATED: Templates are now preloaded during server startup.
        This method is kept for development/debugging purposes only.
        Use template names directly with render_template() instead.
        """
        import warnings
        warnings.warn(
            "load_template_file() is deprecated. Templates are preloaded during startup. "
            "Use template names directly with render_template().",
            DeprecationWarning,
            stacklevel=2
        )

        for template_dir in self.template_dirs:
            file_path = Path(template_dir) / filename
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # Cache the content and track modification time
                    self.template_cache[filename] = content
                    self._loaded_files[filename] = file_path.stat().st_mtime

                    # Load templates into engine
                    self.engine.load_template(filename, content)

                    return content
                except Exception as e:
                    raise TemplateError(f"Error loading template file '{filename}': {e}")

        raise TemplateNotFoundError(f"Template file '{filename}' not found in any template directory")
    
    def load_template_from_string(self, template_name: str, content: str):
        """Load template from string content"""
        try:
            self.engine.load_template(template_name, content)
            self.template_cache[template_name] = content
        except Exception as e:
            raise TemplateError(f"Error loading template '{template_name}': {e}")
    
    def render_template(self, template_name: str, context: Dict[str, Any]) -> str:
        """Render a template with given context"""
        try:
            return self.engine.render_template(template_name, context)
        except TemplateRenderError as e:
            # If template not found, try to load it automatically
            if "not found" in str(e).lower():
                try:
                    # Try to load template file with .xml extension
                    template_file = f"{template_name}.xml"
                    self.load_template_file(template_file)
                    # Retry rendering after loading
                    return self.engine.render_template(template_name, context)
                except (TemplateNotFoundError, FileNotFoundError):
                    # If still can't find template, raise original error
                    pass
            raise TemplateError(f"Error rendering template '{template_name}': {e}")
        except Exception as e:
            raise TemplateError(f"Error rendering template '{template_name}': {e}")
    
    def template_exists(self, template_name: str) -> bool:
        """
        Check if a template exists

        .. deprecated::
            This method is deprecated. Template existence is now handled automatically
            by render_template() and get_template_info() methods.
        """
        import warnings
        warnings.warn(
            "template_exists() is deprecated. Template existence is handled automatically by render_template().",
            DeprecationWarning,
            stacklevel=2
        )
        return template_name in self.engine.system_templates
    
    def list_templates(self) -> List[str]:
        """List all loaded templates"""
        return list(self.engine.system_templates.keys())
    
    def reload_template_file(self, filename: str):
        """
        Reload a template file if it has been modified

        DEPRECATED: Templates are now preloaded during server startup.
        This method is kept for development/debugging purposes only.
        """
        import warnings
        warnings.warn(
            "reload_template_file() is deprecated. Templates are preloaded during startup.",
            DeprecationWarning,
            stacklevel=2
        )

        for template_dir in self.template_dirs:
            file_path = Path(template_dir) / filename
            if file_path.exists():
                current_mtime = file_path.stat().st_mtime
                cached_mtime = self._loaded_files.get(filename, 0)

                if current_mtime > cached_mtime:
                    # File has been modified, reload it
                    self.load_template_file(filename)
                    return True

        return False
    
    def auto_reload_templates(self):
        """
        Check and reload all modified template files

        DEPRECATED: Templates are now preloaded during server startup.
        This method is kept for development/debugging purposes only.
        """
        import warnings
        warnings.warn(
            "auto_reload_templates() is deprecated. Templates are preloaded during startup.",
            DeprecationWarning,
            stacklevel=2
        )

        reloaded = []
        for filename in list(self._loaded_files.keys()):
            if self.reload_template_file(filename):
                reloaded.append(filename)
        return reloaded
    
    def clear_cache(self):
        """Clear template cache"""
        self.template_cache.clear()
        self._loaded_files.clear()
        self.engine.clear_cache()

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics"""
        stats = self.engine.get_cache_stats()
        stats.update({
            'template_files_loaded': len(self._loaded_files),
            'template_content_cached': len(self.template_cache),
            'caching_enabled': self.enable_caching
        })
        return stats

    def set_cache_enabled(self, enabled: bool) -> None:
        """Enable or disable caching"""
        self.enable_caching = enabled
        self.engine.set_cache_enabled(enabled)

    def set_debugging_enabled(self, enabled: bool) -> None:
        """Enable or disable debugging"""
        self.enable_debugging = enabled
        self.engine.set_debugging_enabled(enabled)

    def get_debug_info(self) -> Dict[str, Any]:
        """Get debugging information"""
        return self.engine.get_debug_info()

    def generate_debug_report(self) -> str:
        """Generate a debug report"""
        if self.engine.debugger:
            return self.engine.debugger.format_debug_report()
        return "Debugging is disabled"

    def set_security_enabled(self, enabled: bool) -> None:
        """Enable or disable security"""
        self.enable_security = enabled
        self.engine.set_security_enabled(enabled)

    def get_security_info(self) -> Dict[str, Any]:
        """Get security information"""
        return self.engine.get_security_info()

    def get_security_headers(self) -> Dict[str, str]:
        """Get security headers for HTTP responses"""
        return self.engine.get_security_headers()

    def validate_template_security(self, template_content: str) -> List[str]:
        """Validate template content for security issues"""
        return self.engine.validate_template_security(template_content)
    
    def get_template_info(self, template_name: str) -> Dict[str, Any]:
        """Get information about a template"""
        # Check if template exists in engine
        exists = template_name in self.engine.system_templates

        if not exists:
            # Try to load template automatically
            try:
                template_file = f"{template_name}.xml"
                self.load_template_file(template_file)
                exists = True
            except (TemplateNotFoundError, FileNotFoundError):
                raise TemplateNotFoundError(f"Template '{template_name}' not found")

        return {
            'name': template_name,
            'exists': exists,
            'cached': template_name in self.template_cache,
            'content_length': len(self.template_cache.get(template_name, ''))
        }


class AsyncTemplateManager(TemplateManager):
    """Async version of TemplateManager for use with FastAPI"""

    async def load_all_templates_async(self) -> int:
        """Async version of load_all_templates"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.load_all_templates)

    async def load_template_file_async(self, filename: str) -> str:
        """
        Async version of load_template_file

        DEPRECATED: Templates are now preloaded during server startup.
        This method is kept for development/debugging purposes only.
        """
        import warnings
        warnings.warn(
            "load_template_file_async() is deprecated. Templates are preloaded during startup.",
            DeprecationWarning,
            stacklevel=2
        )

        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.load_template_file, filename)

    async def render_template_async(self, template_name: str, context: Dict[str, Any]) -> str:
        """Async version of render_template"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.render_template, template_name, context)

    async def auto_reload_templates_async(self) -> List[str]:
        """
        Async version of auto_reload_templates

        DEPRECATED: Templates are now preloaded during server startup.
        This method is kept for development/debugging purposes only.
        """
        import warnings
        warnings.warn(
            "auto_reload_templates_async() is deprecated. Templates are preloaded during startup.",
            DeprecationWarning,
            stacklevel=2
        )

        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.auto_reload_templates)

    async def get_cache_stats_async(self) -> Dict[str, Any]:
        """Async version of get_cache_stats"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.get_cache_stats)

    async def get_security_info_async(self) -> Dict[str, Any]:
        """Async version of get_security_info"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.get_security_info)

    async def get_security_headers_async(self) -> Dict[str, str]:
        """Async version of get_security_headers"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.get_security_headers)

    async def validate_template_security_async(self, template_content: str) -> List[str]:
        """Async version of validate_template_security"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.validate_template_security, template_content)


# Global template manager instance
template_manager = AsyncTemplateManager()


def get_template_manager() -> AsyncTemplateManager:
    """Get the global template manager instance"""
    return template_manager


def render_template(template_name: str, context: Dict[str, Any]) -> str:
    """Convenience function to render a template"""
    return template_manager.render_template(template_name, context)


async def render_template_async(template_name: str, context: Dict[str, Any]) -> str:
    """Convenience function to render a template asynchronously"""
    return await template_manager.render_template_async(template_name, context)
