<?xml version="1.0" encoding="utf-8"?>
<templates id="template_system_database_card" xml:space="preserve">

    <!-- Database Card Component -->
    <t t-name="system.database_card">
        <div class="group relative bg-white border border-gray-200 rounded-2xl overflow-hidden cursor-pointer transition-all duration-500 hover:shadow-2xl hover:scale-[1.02] hover:border-transparent" onclick="connectToDatabase(this.dataset.dbname)" t-att-data-dbname="db['name']">
            <!-- Gradient overlay on hover -->
            <div class="absolute inset-0 bg-gradient-to-br from-blue-50/0 via-purple-50/0 to-indigo-50/0 group-hover:from-blue-50/30 group-hover:via-purple-50/20 group-hover:to-indigo-50/30 transition-all duration-500 pointer-events-none"></div>

            <!-- Top accent gradient -->
            <div class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-indigo-500 opacity-0 group-hover:opacity-100 transition-all duration-500"></div>

            <!-- Card content -->
            <div class="relative p-6">
                <!-- Database Header -->
                <div class="flex items-start justify-between mb-6">
                    <div class="flex items-center space-x-4 flex-1 min-w-0">
                        <!-- Enhanced database icon with gradient -->
                        <div class="relative">
                            <div class="w-14 h-14 bg-gradient-to-br from-blue-500 via-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center text-white text-xl shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                                <i class="fas fa-database"></i>
                            </div>
                            <!-- Status indicator dot -->
                            <div t-att-class="'absolute -top-1 -right-1 w-4 h-4 rounded-full border-2 border-white shadow-sm ' + ('bg-green-500' if db['init_status'] == 'ready' else 'bg-yellow-500')">
                                <div t-att-class="'w-full h-full rounded-full animate-ping ' + ('bg-green-400' if db['init_status'] == 'ready' else 'bg-yellow-400')"></div>
                            </div>
                        </div>

                        <div class="flex-1 min-w-0">
                            <h3 class="text-xl font-bold text-gray-900 truncate mb-1 group-hover:text-blue-700 transition-colors duration-300" t-esc="db['name']"/>
                            <p class="text-sm text-gray-500 truncate" t-esc="db['encoding'] + ' • ' + db['owner']"/>

                            <!-- Status badge -->
                            <div class="mt-2">
                                <span t-att-class="'inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold shadow-sm ' + ('bg-green-100 text-green-800 border border-green-200' if db['init_status'] == 'ready' else 'bg-yellow-100 text-yellow-800 border border-yellow-200')" t-att-title="db['init_message']">
                                    <i t-att-class="'fas mr-1.5 ' + db['init_icon']"></i>
                                    <span t-esc="'Ready' if db['init_status'] == 'ready' else 'Setup Required'"/>
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Delete Button (Dev Mode Only) -->
                    <t t-if="is_development_mode">
                        <button class="p-2 text-red-400 hover:text-red-600 hover:bg-red-50 rounded-xl transition-all duration-200 opacity-0 group-hover:opacity-100"
                                onclick="event.stopPropagation(); deleteDatabase(this.dataset.dbname)"
                                t-att-data-dbname="db['name']"
                                title="Delete Database (Dev Mode)">
                            <i class="fas fa-trash text-sm"></i>
                        </button>
                    </t>
                </div>

                <!-- Enhanced Statistics Grid -->
                <div class="grid grid-cols-2 gap-3 mb-6">
                    <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200/50 group-hover:from-blue-100 group-hover:to-blue-200 transition-all duration-300">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center text-white text-xs shadow-sm">
                                    <i class="fas fa-hdd"></i>
                                </div>
                                <span class="text-xs font-semibold text-blue-700">Size</span>
                            </div>
                            <span class="text-sm font-bold text-blue-900" t-esc="db['size']"/>
                        </div>
                    </div>

                    <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-4 border border-green-200/50 group-hover:from-green-100 group-hover:to-green-200 transition-all duration-300">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center text-white text-xs shadow-sm">
                                    <i class="fas fa-table"></i>
                                </div>
                                <span class="text-xs font-semibold text-green-700">Tables</span>
                            </div>
                            <span class="text-sm font-bold text-green-900" t-esc="db['table_count']"/>
                        </div>
                    </div>

                    <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-4 border border-purple-200/50 group-hover:from-purple-100 group-hover:to-purple-200 transition-all duration-300">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center text-white text-xs shadow-sm">
                                    <i class="fas fa-plug"></i>
                                </div>
                                <span class="text-xs font-semibold text-purple-700">Connections</span>
                            </div>
                            <span class="text-sm font-bold text-purple-900" t-esc="db['active_connections']"/>
                        </div>
                    </div>

                    <div class="bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl p-4 border border-orange-200/50 group-hover:from-orange-100 group-hover:to-orange-200 transition-all duration-300">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center text-white text-xs shadow-sm">
                                    <i class="fas fa-calendar"></i>
                                </div>
                                <span class="text-xs font-semibold text-orange-700">Created</span>
                            </div>
                            <span class="text-xs font-bold text-orange-900" t-esc="db['created_display'] or 'Unknown'"/>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Registry Status -->
                <t t-if="db['has_memory_registry']">
                    <div class="mb-6 p-4 bg-gradient-to-r from-indigo-50 to-blue-50 border border-indigo-200 rounded-xl">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-indigo-500 rounded-lg flex items-center justify-center text-white text-xs shadow-sm">
                                <i class="fas fa-memory"></i>
                            </div>
                            <span class="text-sm font-semibold text-indigo-800">Memory Registry Active</span>
                            <div class="flex-1"></div>
                            <div class="flex space-x-1">
                                <div class="w-2 h-2 bg-indigo-500 rounded-full animate-pulse"></div>
                                <div class="w-2 h-2 bg-indigo-400 rounded-full animate-pulse [animation-delay:0.2s]"></div>
                                <div class="w-2 h-2 bg-indigo-300 rounded-full animate-pulse [animation-delay:0.4s]"></div>
                            </div>
                        </div>
                    </div>
                </t>

                <!-- Enhanced Action Button -->
                <t t-if="db['init_status'] == 'ready'">
                    <button class="w-full px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl text-sm font-semibold hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-300 flex items-center justify-center space-x-3 shadow-lg hover:shadow-xl group-hover:scale-[1.02]">
                        <i class="fas fa-rocket text-lg"></i>
                        <span>Connect to Database</span>
                        <i class="fas fa-arrow-right text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
                    </button>
                </t>
                <t t-else="">
                    <button class="w-full px-6 py-4 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-xl text-sm font-semibold hover:from-yellow-600 hover:to-orange-600 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 transition-all duration-300 flex items-center justify-center space-x-3 shadow-lg hover:shadow-xl group-hover:scale-[1.02]" onclick="initializeDatabase(this.dataset.dbname)" t-att-data-dbname="db['name']">
                        <i class="fas fa-cog text-lg"></i>
                        <span>Initialize Database</span>
                        <i class="fas fa-arrow-right text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
                    </button>
                </t>
            </div>
        </div>
    </t>

</templates>
