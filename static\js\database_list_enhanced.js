/**
 * Database List Enhanced - JavaScript functionality
 * Handles modal interactions, database operations, and UI behaviors
 */

// Tailwind CSS configuration
tailwind.config = {
    theme: {
        extend: {
            fontFamily: {
                'sans': ['Inter', 'system-ui', 'sans-serif'],
            },
            colors: {
                'primary': {
                    50: '#eff6ff',
                    500: '#3b82f6',
                    600: '#2563eb',
                    700: '#1d4ed8',
                }
            }
        }
    }
};

// Modal functions
function showCreateDatabaseModal() {
    const modal = document.getElementById('createDatabaseModal');
    modal.classList.remove('hidden');
    modal.classList.add('flex');
    document.getElementById('dbName').focus();
}

function hideCreateDatabaseModal() {
    const modal = document.getElementById('createDatabaseModal');
    modal.classList.add('hidden');
    modal.classList.remove('flex');
    document.getElementById('createDatabaseForm').reset();
}

// Form submission
async function handleCreateDatabase(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const data = {
        name: formData.get('name'),
        language: formData.get('language'),
        demo: formData.has('demo')
    };

    try {
        const response = await fetch('/api/databases', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (response.ok && result.success) {
            hideCreateDatabaseModal();
            alert('Database created successfully! Redirecting...');
            window.location.href = '/home?db=' + encodeURIComponent(data.name);
        } else {
            throw new Error(result.detail || result.message || 'Failed to create database');
        }
    } catch (error) {
        console.error('Error creating database:', error);
        alert('Failed to create database: ' + error.message);
    }
}

function connectToDatabase(dbName) {
    // Add loading state to the clicked card
    const card = document.querySelector(`[data-dbname="${dbName}"]`);
    if (card) {
        const button = card.querySelector('button');
        if (button) {
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Connecting...';
            button.disabled = true;
            button.classList.add('opacity-75');
        }
    }

    // Navigate to the database
    window.location.href = '/home?db=' + encodeURIComponent(dbName);
}

function initializeDatabase(dbName) {
    if (confirm('This will initialize the database with the base module. This process may take a few minutes. Continue?')) {
        // Add loading state to the clicked card
        const card = document.querySelector(`[data-dbname="${dbName}"]`);
        if (card) {
            const button = card.querySelector('button');
            if (button) {
                button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Initializing...';
                button.disabled = true;
                button.classList.add('opacity-75');
            }
        }

        // For now, redirect to home which will trigger initialization through middleware
        // In a full implementation, you might want to call a specific initialization endpoint
        alert('Database initialization will begin when you connect. Please wait for the process to complete.');
        window.location.href = '/home?db=' + encodeURIComponent(dbName);
    }
}

async function deleteDatabase(dbName) {
    if (confirm(`Are you sure you want to delete database "${dbName}"? This action cannot be undone.`)) {
        const confirmText = prompt('This will permanently delete all data in the database. Type "DELETE" to confirm:');
        if (confirmText === 'DELETE') {
            try {
                const response = await fetch(`/api/databases/${encodeURIComponent(dbName)}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    alert('Database deleted successfully');
                    window.location.reload();
                } else {
                    throw new Error(result.detail || result.message || 'Failed to delete database');
                }
            } catch (error) {
                console.error('Error deleting database:', error);
                alert('Failed to delete database: ' + error.message);
            }
        } else if (confirmText !== null) {
            alert('Deletion cancelled. You must type "DELETE" exactly to confirm.');
        }
    }
}

function refreshDatabases() {
    window.location.reload();
}

// Search functionality
function filterDatabases() {
    const searchTerm = document.getElementById('database-search').value.toLowerCase();
    const cards = document.querySelectorAll('[data-dbname]');

    cards.forEach(card => {
        const dbName = card.dataset.dbname.toLowerCase();
        const shouldShow = dbName.includes(searchTerm);
        card.style.display = shouldShow ? '' : 'none';
    });
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Handle modal close on backdrop click
    const modal = document.getElementById('createDatabaseModal');
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                hideCreateDatabaseModal();
            }
        });
    }

    // Handle escape key to close modal
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const modal = document.getElementById('createDatabaseModal');
            if (modal && !modal.classList.contains('hidden')) {
                hideCreateDatabaseModal();
            }
        }
    });
});
